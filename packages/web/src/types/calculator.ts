/**
 * Types for calculator components
 */

import { fabric } from 'fabric'
import { PhysicalDimensions } from './core'

/**
 * Handler for object modified events in the calculator
 */
export type ObjectModifiedHandler = (obj: fabric.Object) => void

/**
 * Scaling options for calculator canvas objects
 */
export interface CalculatorScalingOptions {
  scaleX?: number
  scaleY?: number
  width?: number
  height?: number
}

/**
 * Canvas center coordinates
 */
export interface CanvasCenter {
  x: number
  y: number
}

/**
 * Estimate response from the API
 */
export interface EstimateResponse {
  stitchCount: number
  price?: number
  estimatedTime?: string
  underlayStitches?: number
  actualAreaMm2?: number
  designWidth?: number
  designHeight?: number
  totalPixels?: number
  filledPixels?: number
  fillRatio?: number
  garmentType?: string
  isMockData?: { type: 'fallback' | 'simulated'; reason?: string }
  [key: string]: any
}

/**
 * Canvas props
 */
export interface CanvasProps {
  onDimensionsChange?: (dimensions: PhysicalDimensions) => void
  onStitchCountChange?: (stitchCount: number) => void
  backgroundImage?: string | { url: string; dimensions: any }
  units: 'in' | 'mm'
  [key: string]: any
}

/**
 * Panning canvas interface
 */
export interface PanningCanvas extends fabric.Canvas {
  isDragging?: boolean
  lastPosX?: number
  lastPosY?: number
  viewportTransform?: number[]
  wrapperEl?: HTMLElement
}

/**
 * Scaling action handler
 */
export type ScalingActionHandler = (
  eventData: MouseEvent,
  transform: fabric.Transform,
  x: number,
  y: number,
) => boolean

/**
 * Scaling mouse up handler
 */
export type ScalingMouseUpHandler = (eventData: MouseEvent, transform: fabric.Transform) => boolean

/**
 * Canvas debugger interface
 */
export interface CanvasDebugger {
  logCanvasState?: () => void
  logObjectState?: (obj: fabric.Object) => void
  showGrid?: (size?: number, color?: string) => void
  hideGrid?: () => void
  // Core debug methods
  log: (message: string, data?: unknown) => void
  error: (message: string, error?: unknown) => void
  warn: (message: string, data?: unknown) => void
  group: (name: string) => void
  groupEnd: () => void
}

/**
 * Load main image parameters
 * @deprecated Use LoadMainImageParams from calculator.d.ts instead
 */
export interface LoadMainImageParams {
  imageUrl: string
  canvas: fabric.Canvas
  callback?: (img: fabric.Image) => void
  // Additional parameters
  url?: string
  dimensions?: any
  units?: 'in' | 'mm'
  currentRef?: fabric.Image
  onDimensionsChange?: (dimensions: { width: number; height: number }) => void
  processingId?: string
  renderHelper?: {
    renderCanvas: () => void
  }
}

/**
 * Load background image parameters
 */
export interface LoadBackgroundImageParams {
  url: string
  canvas: fabric.Canvas
  callback?: (img: fabric.Image) => void
  // Additional parameters
  dimensions?: any
  units?: 'in' | 'mm'
  currentRef?: fabric.Image
}

/**
 * Update existing image parameters
 */
export interface UpdateExistingImageParams {
  img: fabric.Image
  dimensions: any
  units: 'in' | 'mm'
  canvas: fabric.Canvas
  callback?: (img: fabric.Image) => void
}

/**
 * Zoom state
 */
export interface ZoomState {
  scale: number
  panX?: number
  panY?: number
  // Additional parameters
  onScaleChange?: (scale: number) => void
}

/**
 * Zoom constants
 */
export interface ZoomConstants {
  MIN_ZOOM: number
  MAX_ZOOM: number
  WHEEL_ZOOM_FACTOR: number
  TOUCH_ZOOM_FACTOR: number
  ZOOM_STEP?: number // Make this optional
}

/**
 * Canvas interaction options
 */
export interface CanvasInteractionOptions {
  enablePanning?: boolean
  enableZooming?: boolean
  enableSelection?: boolean
  // Additional parameters
  onZoomChange?: (scale: number) => void
}

/**
 * Touch gesture event
 */
export interface TouchGestureEvent extends TouchEvent {
  scale: number
  rotation: number
}

/**
 * Background removal service interface
 */
export interface BackgroundRemovalService {
  type: 'remote' | 'local' | 'imgly'
  removeBackground: (
    image: File | Blob,
    progressCallback?: (phase: string, current: number, total: number) => void,
  ) => Promise<Blob | any> // Allow any return type for flexibility
  ensureInitialized: () => Promise<void>
  hasHealthyEndpoints?: () => boolean
  // Optional methods
  addEndpoint?: (endpoint: any) => void
  removeEndpoint?: (url: string) => void
  dispose?: () => void
  detectBackground?: (file: File) => Promise<any>
}
