/**
 * Tests for the stitch-file-preview client component
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import FabricEmbroideryClient from '../client'

// Mock the SUPPORTED_FORMATS import
vi.mock('@/types/embroidery-shared', () => ({
  SUPPORTED_FORMATS: ['dst', 'pes', 'pec', 'jef', 'exp', 'vp3'],
}))

// Mock the EmbroideryFileMetadata type
vi.mock('@stitchestimate/shared', () => ({
  EmbroideryFileMetadata: {},
}))

// Mock the FabricEmbroideryViewer component
vi.mock('@/components/embroidery/FabricEmbroideryViewer', () => {
  return {
    default: function MockFabricEmbroideryViewer(props: any) {
      return (
        <div data-testid="fabric-embroidery-viewer">
          <div>Mock Embroidery Viewer</div>
          <div>File: {props.file?.name || 'No file'}</div>
          <div>Line Width: {props.globalLineWidth}</div>
          <div>3D Effect: {props.globalUse3dEffect ? 'Yes' : 'No'}</div>
          <div>Resolution Scale: {props.globalResolutionScale}</div>
          <div>Is Preview: {props.isPreview ? 'Yes' : 'No'}</div>
          <div>Show Controls: {props.showControls ? 'Yes' : 'No'}</div>
          <button
            data-testid="trigger-metadata-change"
            onClick={() =>
              props.onMetadataChange?.({
                format: 'dst',
                width: 100,
                height: 100,
                stitchCount: 1000,
                colorCount: 5,
                filename: props.file?.name || 'test.dst',
              })
            }
          >
            Trigger Metadata Change
          </button>
        </div>
      )
    },
  }
})

// Mock the UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
  CardHeader: ({ children, className }: any) => (
    <div data-testid="card-header" className={className}>
      {children}
    </div>
  ),
  CardTitle: ({ children, className }: any) => (
    <div data-testid="card-title" className={className}>
      {children}
    </div>
  ),
  CardDescription: ({ children, className }: any) => (
    <div data-testid="card-description" className={className}>
      {children}
    </div>
  ),
  CardContent: ({ children, className }: any) => (
    <div data-testid="card-content" className={className}>
      {children}
    </div>
  ),
}))

vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange }: any) => (
    <div data-testid="dialog" data-open={open}>
      {children}
      <button data-testid="dialog-close" onClick={() => onOpenChange(false)}>
        Close Dialog
      </button>
    </div>
  ),
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <div data-testid="dialog-title">{children}</div>,
  DialogDescription: ({ children }: any) => <div data-testid="dialog-description">{children}</div>,
}))

vi.mock('@/components/ui/slider', () => ({
  Slider: ({ value, onValueChange }: any) => (
    <div data-testid="slider" data-value={value}>
      <button data-testid="slider-change" onClick={() => onValueChange([value[0] + 0.1])}>
        Change Value
      </button>
    </div>
  ),
}))

vi.mock('@/components/ui/switch', () => ({
  Switch: ({ checked, onCheckedChange }: any) => (
    <div data-testid="switch" data-checked={checked}>
      <button data-testid="switch-toggle" onClick={() => onCheckedChange(!checked)}>
        Toggle
      </button>
    </div>
  ),
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className }: any) => (
    <button data-testid="button" className={className} onClick={onClick}>
      {children}
    </button>
  ),
}))

// Mock the lodash debounce function
vi.mock('lodash', () => ({
  debounce: (fn: any) => fn,
}))

describe('Fabric Embroidery Client', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Mock the File constructor
    global.File = class MockFile {
      name: string
      type: string
      size: number

      constructor(bits: any[], name: string, options?: any) {
        this.name = name
        this.type = options?.type || ''
        this.size = bits.length
      }
    } as any

    // Mock scrollIntoView
    Element.prototype.scrollIntoView = vi.fn()
  })

  it('renders the upload area when no files are uploaded', () => {
    render(<FabricEmbroideryClient />)

    // Check for the upload title
    expect(screen.getByText('Upload Embroidery Files')).toBeInTheDocument()

    // Check for the upload description
    expect(
      screen.getByText('Drag and drop or click to upload embroidery files'),
    ).toBeInTheDocument()

    // Check that no files are displayed
    expect(screen.queryByTestId('fabric-embroidery-viewer')).not.toBeInTheDocument()
  })

  it('handles file upload via input change', async () => {
    render(<FabricEmbroideryClient />)

    // Create a mock file
    const file = new File(['test content'], 'test.dst', { type: 'application/octet-stream' })

    // Get the file input
    const input = screen.getByTestId('file-input')

    // Simulate file upload
    fireEvent.change(input, { target: { files: [file] } })

    // Wait for the file to be processed
    await waitFor(() => {
      expect(screen.getByText(/File Uploaded/)).toBeInTheDocument()
    })

    // Check that the file viewer is displayed
    expect(screen.getByTestId('fabric-embroidery-viewer')).toBeInTheDocument()
    expect(screen.getByText('File: test.dst')).toBeInTheDocument()
  })

  it('handles global settings changes', async () => {
    render(<FabricEmbroideryClient />)

    // Upload a file first
    const file = new File(['test content'], 'test.dst', { type: 'application/octet-stream' })
    const input = screen.getByTestId('file-input')
    fireEvent.change(input, { target: { files: [file] } })

    // Wait for the file to be processed
    await waitFor(() => {
      expect(screen.getByText(/File Uploaded/)).toBeInTheDocument()
    })

    // Open global settings
    const settingsButton = screen.getByTestId('global-settings-button')
    fireEvent.click(settingsButton)

    // Check that the dialog is open
    const dialogs = screen.getAllByTestId('dialog')
    const globalSettingsDialog = dialogs.find((dialog) =>
      dialog.textContent?.includes('Global Render Settings'),
    )
    expect(globalSettingsDialog).toHaveAttribute('data-open', 'true')

    // Change line width
    const sliderChanges = screen.getAllByTestId('slider-change')
    fireEvent.click(sliderChanges[0])

    // Change 3D effect
    const toggleSwitches = screen.getAllByTestId('switch-toggle')
    fireEvent.click(toggleSwitches[0])

    // Change resolution scale
    if (sliderChanges.length > 1) {
      fireEvent.click(sliderChanges[1])
    }

    // Close the dialog
    const closeButtons = screen.getAllByTestId('dialog-close')
    const globalSettingsCloseButton = closeButtons[0]
    fireEvent.click(globalSettingsCloseButton)

    // Check that the viewer has the updated settings
    expect(screen.getByTestId('fabric-embroidery-viewer')).toBeInTheDocument()
    expect(screen.getByText('3D Effect: Yes')).toBeInTheDocument()
  })

  it('handles file removal', async () => {
    render(<FabricEmbroideryClient />)

    // Upload a file
    const file = new File(['test content'], 'test.dst', { type: 'application/octet-stream' })
    const input = screen.getByTestId('file-input')
    fireEvent.change(input, { target: { files: [file] } })

    // Wait for the file to be processed
    await waitFor(() => {
      expect(screen.getByText(/File Uploaded/)).toBeInTheDocument()
    })

    // Check that the file viewer is displayed
    expect(screen.getByTestId('fabric-embroidery-viewer')).toBeInTheDocument()

    // Remove the file
    const removeButton = screen.getByTestId('remove-file-button')
    fireEvent.click(removeButton)

    // Wait for the file to be removed
    await waitFor(() => {
      expect(screen.queryByTestId('fabric-embroidery-viewer')).not.toBeInTheDocument()
    })

    // Check that we're back to the upload state
    expect(screen.getByText('Upload Embroidery Files')).toBeInTheDocument()
  })
})
