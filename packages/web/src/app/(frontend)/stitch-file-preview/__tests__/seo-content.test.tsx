/**
 * Tests for the stitch-file-preview SEO content component
 */

import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import SEOContent from '../seo-content'

// Mock the SUPPORTED_FORMATS import
vi.mock('@/types/embroidery-shared', () => ({
  SUPPORTED_FORMATS: ['dst', 'pes', 'pec', 'jef', 'exp', 'vp3'],
}))

describe('Stitch File Preview SEO Content', () => {
  it('renders the SEO content with supported formats', () => {
    render(<SEOContent />)

    // Check for the title
    expect(
      screen.getByText('Embroidery Stitch File Preview - Professional Design Tool'),
    ).toBeInTheDocument()

    // Check for the supported formats section
    expect(screen.getByText('Supported File Formats')).toBeInTheDocument()

    // Check that all supported formats are rendered
    expect(screen.getByText('DST')).toBeInTheDocument()
    expect(screen.getByText('PES')).toBeInTheDocument()
    expect(screen.getByText('PEC')).toBeInTheDocument()
    expect(screen.getByText('JEF')).toBeInTheDocument()
    expect(screen.getByText('EXP')).toBeInTheDocument()
    expect(screen.getByText('VP3')).toBeInTheDocument()

    // Check for key features section
    expect(screen.getByText('Key Features')).toBeInTheDocument()

    // Check for advantages section
    expect(screen.getByText('Advantages of Our Preview Tool')).toBeInTheDocument()

    // Check for FAQ section
    expect(screen.getByText('Frequently Asked Questions')).toBeInTheDocument()
  })
})
