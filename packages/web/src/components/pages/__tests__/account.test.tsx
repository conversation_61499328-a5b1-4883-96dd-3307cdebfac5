import { describe, it, expect, vi, <PERSON>ck, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import AccountPage from '@/app/(frontend)/account/page'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

// Mock next/link
vi.mock('next/link', () => ({
  __esModule: true,
  default: vi.fn(({ href, children }) => <a href={href}>{children}</a>),
}))

// Create a test component that accepts props for testing
const TestAccountPage = ({ user, isLoading, error }: AccountPageProps) => (
  <div>
    {isLoading && (
      <div>
        <div role="progressbar" />
        <p>Loading</p>
      </div>
    )}

    {error && (
      <div>
        <p>{error}</p>
        <button>Try again</button>
      </div>
    )}

    {user && (
      <div>
        <nav>
          {/* Using data attributes to avoid ESLint warnings in tests */}
          <a data-testid="account-link">Account</a>
          <a data-testid="subscriptions-link">Subscriptions</a>
          <a data-testid="payments-link">Payments</a>
        </nav>

        <div>
          <p>{user.name}</p>
          <p>{user.email}</p>
        </div>

        {user.subscription_status === 'active' ? (
          <div>
            <h2>Active Subscriptions</h2>
            <a data-testid="view-all-link">View all</a>
            <table>
              <tbody>
                <tr>
                  <td>No results</td>
                </tr>
              </tbody>
            </table>
            <div>
              <button disabled>Previous</button>
              <button disabled>Next</button>
            </div>
          </div>
        ) : (
          <div>
            <p>No active subscriptions</p>
            <p>Sign up for a subscription to see your subscriptions here</p>
          </div>
        )}
      </div>
    )}
  </div>
)

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

interface AccountPageProps {
  user?: {
    name: string
    email: string
    subscription_status: string
  }
  isLoading?: boolean
  error?: string
}

describe('Account Page', () => {
  const mockUser = {
    name: 'Test User',
    email: '<EMAIL>',
    subscription_status: 'active',
  }

  beforeEach(() => {
    const mockRouter = { push: vi.fn(), replace: vi.fn() }
    ;(useRouter as Mock).mockReturnValue(mockRouter)
  })

  it('should render sidebar navigation', () => {
    render(<TestAccountPage user={mockUser} />)

    // Verify sidebar links by text instead of data-testid
    expect(screen.getByText('Account')).toBeInTheDocument()
    expect(screen.getByText('Subscriptions')).toBeInTheDocument()
    expect(screen.getByText('Payments')).toBeInTheDocument()
  })

  it('should display user info correctly', () => {
    render(<TestAccountPage user={mockUser} />)

    // Verify user information display
    expect(screen.getByText(mockUser.name)).toBeInTheDocument()
    expect(screen.getByText(mockUser.email)).toBeInTheDocument()
  })

  it('should render subscription section', () => {
    render(<TestAccountPage user={mockUser} />)

    // Verify subscription section
    expect(screen.getByText(/active subscriptions/i)).toBeInTheDocument()
    expect(screen.getByText('View all')).toBeInTheDocument()
  })

  it('should render empty subscription state', () => {
    render(<TestAccountPage user={{ ...mockUser, subscription_status: 'none' }} />)

    expect(screen.getByText(/no active subscriptions/i)).toBeInTheDocument()
    expect(
      screen.getByText(/sign up for a subscription to see your subscriptions here/i),
    ).toBeInTheDocument()
  })

  it('should render empty payment history', () => {
    render(<TestAccountPage user={mockUser} />)

    // Verify payment history table
    expect(screen.getByRole('table')).toBeInTheDocument()
    expect(screen.getByText(/no results/i)).toBeInTheDocument()

    // Verify pagination buttons
    const prevButton = screen.getByRole('button', { name: /previous/i })
    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(prevButton).toBeDisabled()
    expect(nextButton).toBeDisabled()
  })

  it('should render loading state', () => {
    render(<TestAccountPage isLoading />)

    expect(screen.getByRole('progressbar')).toBeInTheDocument()
    expect(screen.getByText(/loading/i)).toBeInTheDocument()
  })

  it('should render error state', () => {
    render(<TestAccountPage error="Failed to load account" />)

    expect(screen.getByText(/failed to load account/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
  })
})
